@import '../../styles/variables.less';

.security-dashboard {
  width: 100%;
  height: 100vh;
  position: relative;
  background: radial-gradient(
    ellipse at center,
    #0a1a2e 0%,
    #16213e 30%,
    #0f1419 70%,
    #000000 100%
  );
  color: #ffffff;
  font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
  overflow-x: hidden;
  overflow-y: auto;

  // 主背景纹理
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      radial-gradient(circle at 25% 25%, rgba(0, 212, 255, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(0, 255, 155, 0.03) 0%, transparent 50%),
      linear-gradient(45deg, transparent 49%, rgba(0, 212, 255, 0.02) 50%, transparent 51%);
    background-size:
      200px 200px,
      300px 300px,
      50px 50px;
    z-index: 1;
  }

  // 科技网格覆盖
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
    background-size: 100px 100px;
    opacity: 0.3;
    z-index: 2;
  }

  // 顶部标题区域
  .dashboard-header {
    position: relative;
    z-index: 10;
    height: 120px;
    background: linear-gradient(
      135deg,
      rgba(0, 20, 40, 0.9) 0%,
      rgba(0, 40, 80, 0.7) 50%,
      rgba(0, 20, 40, 0.9) 100%
    );
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 0 0 20px 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 40px;
    position: relative;
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);

    .header-decorative-lines {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      pointer-events: none;

      .line {
        position: absolute;
        height: 2px;
        background: linear-gradient(
          90deg,
          transparent 0%,
          rgba(0, 212, 255, 0.8) 20%,
          rgba(0, 212, 255, 1) 50%,
          rgba(0, 212, 255, 0.8) 80%,
          transparent 100%
        );
        animation: lineFlow 3s ease-in-out infinite;

        &.line-left {
          top: 20px;
          left: 10%;
          width: 30%;
          animation-delay: 0s;
        }

        &.line-center {
          top: 15px;
          left: 35%;
          width: 30%;
          animation-delay: 1s;
        }

        &.line-right {
          top: 25px;
          right: 10%;
          width: 25%;
          animation-delay: 2s;
        }
      }
    }

    .platform-title-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;
      position: relative;

      .title-decorative-elements {
        display: flex;
        align-items: center;
        gap: 20px;
        width: 100%;
        max-width: 600px;

        .title-line {
          flex: 1;
          height: 2px;
          background: linear-gradient(90deg, transparent, #00d4ff, transparent);
          position: relative;

          &::before {
            content: '';
            position: absolute;
            top: -1px;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.3), transparent);
            filter: blur(2px);
          }
        }

        .title-icon {
          font-size: 24px;
          filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.8));
          animation: iconPulse 3s ease-in-out infinite;
        }
      }

      .platform-title {
        font-size: 42px;
        font-weight: 700;
        margin: 0;
        letter-spacing: 0.15em;
        display: flex;
        align-items: center;
        gap: 16px;
        position: relative;

        .title-main {
          background: linear-gradient(135deg, #ffffff 0%, #00d4ff 50%, #ffffff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
          filter: drop-shadow(0 0 15px rgba(0, 212, 255, 0.4));
        }

        .title-separator {
          color: #00d4ff;
          font-size: 32px;
          animation: separatorGlow 2s ease-in-out infinite alternate;
        }

        .title-sub {
          background: linear-gradient(135deg, #00ff9b 0%, #00d4ff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          filter: drop-shadow(0 0 10px rgba(0, 255, 155, 0.3));
        }
      }

      .title-subtitle {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        letter-spacing: 0.1em;

        .subtitle-text {
          font-style: italic;
          font-weight: 300;
        }

        .subtitle-version {
          padding: 4px 12px;
          background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(0, 255, 155, 0.2));
          border: 1px solid rgba(0, 212, 255, 0.4);
          border-radius: 12px;
          font-size: 12px;
          font-weight: 600;
          color: #00d4ff;
          backdrop-filter: blur(10px);
        }
      }

      .title-status-indicators {
        display: flex;
        gap: 24px;
        margin-top: 8px;

        .status-indicator {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 12px;
          color: rgba(255, 255, 255, 0.6);
          transition: all 0.3s ease;

          &.active {
            color: rgba(255, 255, 255, 0.9);
          }

          .indicator-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            position: relative;
            transition: all 0.3s ease;

            &::before {
              content: '';
              position: absolute;
              top: -2px;
              left: -2px;
              right: -2px;
              bottom: -2px;
              border-radius: 50%;
              background: radial-gradient(circle, transparent 40%, rgba(0, 212, 255, 0.3) 70%);
              opacity: 0;
              transition: opacity 0.3s ease;
            }
          }

          &.active .indicator-dot {
            background: #00ff9b;
            box-shadow: 0 0 10px rgba(0, 255, 155, 0.6);
            animation: statusPulse 2s ease-in-out infinite;

            &::before {
              opacity: 1;
              animation: statusRipple 2s ease-in-out infinite;
            }
          }

          &:hover {
            color: rgba(255, 255, 255, 0.9);

            .indicator-dot {
              background: #00d4ff;
              box-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
            }
          }
        }
      }
    }

    .header-info {
      position: absolute;
      right: 40px;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 8px;

      .system-status {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);

        .status-icon {
          color: #00ff9b;
          animation: pulse 2s ease-in-out infinite;
        }

        .status-text {
          max-width: 300px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .current-time {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 6px 16px;
        background: rgba(0, 212, 255, 0.1);
        border: 1px solid rgba(0, 212, 255, 0.3);
        border-radius: 20px;
        backdrop-filter: blur(10px);

        .time-label {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.8);
        }

        .time-value {
          font-size: 16px;
          font-weight: 600;
          color: #00d4ff;
          font-family: 'Courier New', monospace;
          text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }
      }
    }
  }

  // 核心指标区域
  .core-metrics {
    position: relative;
    z-index: 15;
    display: flex;
    justify-content: center;
    gap: 80px;
    padding: 60px 0;
    margin: 0 40px;
    min-height: 120px;

    .metric-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 20px 30px;
      background: linear-gradient(
        135deg,
        rgba(0, 50, 100, 0.3) 0%,
        rgba(0, 30, 60, 0.5) 50%,
        rgba(0, 20, 40, 0.3) 100%
      );
      border: 1px solid rgba(0, 212, 255, 0.3);
      border-radius: 15px;
      backdrop-filter: blur(20px);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
        transition: left 0.5s ease;
      }

      &:hover {
        transform: translateY(-5px);
        border-color: rgba(0, 212, 255, 0.6);
        box-shadow: 0 10px 30px rgba(0, 212, 255, 0.2);

        &::before {
          left: 100%;
        }
      }

      .metric-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        position: relative;

        &.alert-icon {
          background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
          box-shadow: 0 0 20px rgba(255, 107, 107, 0.4);
        }

        &.process-icon {
          background: linear-gradient(135deg, #00d4ff, #5b8fff);
          box-shadow: 0 0 20px rgba(0, 212, 255, 0.4);
        }

        &.resolve-icon {
          background: linear-gradient(135deg, #00ff9b, #00d4aa);
          box-shadow: 0 0 20px rgba(0, 255, 155, 0.4);
        }
      }

      .metric-content {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .metric-value {
          font-size: 32px;
          font-weight: 700;
          color: #ffffff;
          text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
          font-family: 'Courier New', monospace;
        }

        .metric-label {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.8);
          font-weight: 400;
        }
      }
    }
  }

  // 主要内容区域
  .dashboard-main {
    position: relative;
    z-index: 10;
    display: flex;
    padding: 0 40px;
    gap: 40px;

    // 左侧面板
    .left-panel {
      width: 380px;
      display: flex;
      flex-direction: column;
      gap: 20px;
      padding: 20px 0;

      .panel-card {
        background: linear-gradient(
          135deg,
          rgba(0, 30, 60, 0.4) 0%,
          rgba(0, 50, 100, 0.3) 50%,
          rgba(0, 20, 40, 0.4) 100%
        );
        backdrop-filter: blur(20px);
        border: 1px solid rgba(0, 212, 255, 0.2);
        border-radius: 15px;
        padding: 20px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 2px;
          background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.8), transparent);
        }

        &:hover {
          border-color: rgba(0, 212, 255, 0.4);
          box-shadow: 0 8px 25px rgba(0, 212, 255, 0.15);
        }

        .card-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 20px;
          padding-bottom: 12px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);

          .card-icon {
            font-size: 16px;
            color: #00d4ff;
          }

          .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            flex: 1;
            margin-left: 12px;
          }

          .card-controls {
            .control-btn {
              font-size: 14px;
              color: rgba(255, 255, 255, 0.6);
              cursor: pointer;
              transition: color 0.3s ease;

              &:hover {
                color: #00d4ff;
              }
            }
          }
        }

        // 模型训练分析样式
        &.training-analysis {
          .metric-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;

            .metric-item {
              text-align: center;
              padding: 15px;
              background: rgba(0, 0, 0, 0.2);
              border-radius: 10px;
              border: 1px solid rgba(255, 255, 255, 0.1);

              .metric-label {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.7);
                margin-bottom: 8px;
              }

              .metric-value {
                font-size: 24px;
                font-weight: 700;
                color: #ffffff;
                font-family: 'Courier New', monospace;

                &.highlight {
                  color: #00d4ff;
                  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
                }
              }

              .metric-unit {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.6);
                margin-left: 4px;
              }
            }
          }
        }

        // 算力资源分析样式
        &.resource-analysis {
          .resource-meters {
            display: flex;
            justify-content: space-around;
            gap: 15px;

            .meter-item {
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 8px;

              .meter-label {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.8);
                font-weight: 500;
              }

              .circular-meter {
                position: relative;
                width: 80px;
                height: 80px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                .meter-value {
                  font-size: 18px;
                  font-weight: 700;
                  color: #ffffff;
                  font-family: 'Courier New', monospace;
                  z-index: 2;
                }

                .meter-unit {
                  font-size: 10px;
                  color: rgba(255, 255, 255, 0.6);
                  z-index: 2;
                }

                .meter-circle {
                  position: absolute;
                  top: 0;
                  left: 0;
                  width: 100%;
                  height: 100%;
                  transform: rotate(-90deg);
                }
              }
            }
          }
        }

        // 模型调用排行样式
        &.model-ranking {
          .ranking-list {
            .ranking-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 12px 0;
              border-bottom: 1px solid rgba(255, 255, 255, 0.05);

              &:last-child {
                border-bottom: none;
              }

              .rank-info {
                display: flex;
                align-items: center;
                gap: 12px;
                flex: 1;

                .rank-number {
                  width: 20px;
                  height: 20px;
                  background: linear-gradient(135deg, #00d4ff, #5b8fff);
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 10px;
                  font-weight: 600;
                  color: #ffffff;
                }

                .model-name {
                  font-size: 12px;
                  color: #ffffff;
                  font-weight: 500;
                }
              }

              .rank-stats {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 10px;

                .calls {
                  color: #00d4ff;
                  font-weight: 600;
                }

                .percentage {
                  color: #00ff9b;
                  font-weight: 600;
                }

                .trend {
                  color: #ffbb55;
                  font-weight: 600;
                }

                .unit {
                  color: rgba(255, 255, 255, 0.5);
                }
              }
            }
          }
        }
      }
    }

    // 中央安全态势感知中心
    .center-visualization {
      flex: 1;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 500px;
      overflow: hidden;

      .security-command-center {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        perspective: 1000px;

        // 背景网格
        .grid-background {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 1;

          .grid-lines {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
              linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: gridMove 20s linear infinite;
          }

          .grid-dots {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: radial-gradient(circle, rgba(0, 255, 155, 0.3) 1px, transparent 1px);
            background-size: 25px 25px;
            animation: gridMove 30s linear infinite reverse;
          }
        }

        // 中央控制台
        .central-console {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 10;

          .console-base {
            width: 120px;
            height: 120px;
            position: relative;

            .base-glow {
              position: absolute;
              top: -20px;
              left: -20px;
              right: -20px;
              bottom: -20px;
              background: radial-gradient(circle, rgba(0, 212, 255, 0.4) 0%, transparent 70%);
              border-radius: 50%;
              filter: blur(20px);
              animation: consoleGlow 3s ease-in-out infinite alternate;
            }

            .base-surface {
              width: 100%;
              height: 100%;
              background: linear-gradient(
                135deg,
                rgba(0, 40, 80, 0.9) 0%,
                rgba(0, 20, 40, 0.9) 100%
              );
              border: 2px solid rgba(0, 212, 255, 0.6);
              border-radius: 50%;
              box-shadow:
                0 0 30px rgba(0, 212, 255, 0.5),
                inset 0 0 20px rgba(0, 212, 255, 0.2);
            }
          }

          .holographic-display {
            position: absolute;
            top: -80px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 5;

            .holo-screen {
              width: 200px;
              height: 150px;
              position: relative;
              background: rgba(0, 20, 40, 0.8);
              border: 1px solid rgba(0, 212, 255, 0.5);
              border-radius: 10px;
              backdrop-filter: blur(10px);
              box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);

              .screen-frame {
                position: absolute;
                top: -2px;
                left: -2px;
                right: -2px;
                bottom: -2px;
                border: 1px solid rgba(0, 255, 155, 0.3);
                border-radius: 12px;
                background: linear-gradient(
                  45deg,
                  transparent,
                  rgba(0, 255, 155, 0.1),
                  transparent
                );
              }

              .screen-content {
                padding: 15px;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                .threat-radar {
                  width: 100%;
                  height: 100%;
                  position: relative;
                  border-radius: 50%;
                  background: radial-gradient(circle, rgba(0, 212, 255, 0.1) 0%, transparent 70%);

                  .radar-center {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 8px;
                    height: 8px;
                    background: #00ff9b;
                    border-radius: 50%;
                    box-shadow: 0 0 15px rgba(0, 255, 155, 0.8);
                  }

                  .radar-sweep {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 100%;
                    height: 100%;
                    background: conic-gradient(
                      from 0deg,
                      transparent 0deg,
                      rgba(0, 255, 155, 0.3) 30deg,
                      transparent 60deg
                    );
                    border-radius: 50%;
                    animation: radarSweep 3s linear infinite;
                  }

                  .radar-rings {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 100%;
                    height: 100%;

                    .ring {
                      position: absolute;
                      border: 1px solid rgba(0, 212, 255, 0.3);
                      border-radius: 50%;
                      top: 50%;
                      left: 50%;
                      transform: translate(-50%, -50%);

                      &:nth-child(1) {
                        width: 40%;
                        height: 40%;
                      }
                      &:nth-child(2) {
                        width: 70%;
                        height: 70%;
                      }
                      &:nth-child(3) {
                        width: 100%;
                        height: 100%;
                      }
                    }
                  }

                  .threat-dots {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;

                    .threat-dot {
                      position: absolute;
                      width: 6px;
                      height: 6px;
                      border-radius: 50%;
                      animation: threatPulse 2s ease-in-out infinite;

                      &.high {
                        background: #ff4444;
                        box-shadow: 0 0 10px rgba(255, 68, 68, 0.8);
                        top: 30%;
                        left: 60%;
                      }

                      &.medium {
                        background: #ffaa00;
                        box-shadow: 0 0 8px rgba(255, 170, 0, 0.8);
                        top: 70%;
                        left: 40%;
                      }

                      &.low {
                        background: #00ff9b;
                        box-shadow: 0 0 6px rgba(0, 255, 155, 0.8);

                        &:nth-child(3) {
                          top: 50%;
                          left: 80%;
                        }

                        &:nth-child(4) {
                          top: 20%;
                          left: 30%;
                        }
                      }
                    }
                  }
                }
              }
            }

            .holo-label {
              position: absolute;
              top: 160px;
              left: 50%;
              transform: translateX(-50%);
              font-size: 12px;
              color: rgba(255, 255, 255, 0.8);
              background: rgba(0, 0, 0, 0.5);
              padding: 4px 12px;
              border-radius: 12px;
              backdrop-filter: blur(10px);
              border: 1px solid rgba(0, 212, 255, 0.3);
              white-space: nowrap;
            }
          }
        }

        // 安全防护罩
        .security-dome {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 2;

          .dome-structure {
            position: relative;
            width: 400px;
            height: 400px;

            .dome-layer {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              border-radius: 50%;
              border: 1px solid;
              animation: domeRotate 20s linear infinite;

              &.layer-1 {
                width: 100%;
                height: 100%;
                border-color: rgba(0, 212, 255, 0.2);
                animation-duration: 20s;
              }

              &.layer-2 {
                width: 80%;
                height: 80%;
                border-color: rgba(0, 255, 155, 0.3);
                animation-duration: 15s;
                animation-direction: reverse;
              }

              &.layer-3 {
                width: 60%;
                height: 60%;
                border-color: rgba(255, 100, 100, 0.2);
                animation-duration: 25s;
              }
            }
          }

          .dome-energy-flows {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;

            .energy-stream {
              position: absolute;
              width: 2px;
              height: 100px;
              background: linear-gradient(
                to bottom,
                transparent,
                rgba(0, 212, 255, 0.8),
                transparent
              );
              animation: energyFlow 3s ease-in-out infinite;

              &.stream-1 {
                top: 10%;
                left: 50%;
                animation-delay: 0s;
              }

              &.stream-2 {
                top: 50%;
                right: 10%;
                transform: rotate(90deg);
                animation-delay: 0.75s;
              }

              &.stream-3 {
                bottom: 10%;
                left: 50%;
                transform: rotate(180deg);
                animation-delay: 1.5s;
              }

              &.stream-4 {
                top: 50%;
                left: 10%;
                transform: rotate(270deg);
                animation-delay: 2.25s;
              }
            }
          }
        }
      }

      // 数据节点群
      .data-nodes {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 5;

        .node-cluster {
          position: absolute;

          &.cluster-left {
            top: 50%;
            left: 15%;
            transform: translateY(-50%);
          }

          &.cluster-right {
            top: 50%;
            right: 15%;
            transform: translateY(-50%);
          }

          &.cluster-top {
            top: 15%;
            left: 50%;
            transform: translateX(-50%);
          }

          &.cluster-bottom {
            bottom: 15%;
            left: 50%;
            transform: translateX(-50%);
          }

          .node {
            position: relative;
            margin: 10px;
            display: inline-block;

            &.primary {
              .node-core {
                width: 40px;
                height: 40px;
                background: radial-gradient(
                  circle,
                  rgba(0, 212, 255, 0.8) 0%,
                  rgba(0, 150, 200, 0.6) 100%
                );
                border: 2px solid rgba(0, 212, 255, 0.8);
                border-radius: 50%;
                box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
                animation: nodePulse 2s ease-in-out infinite;
              }

              .node-label {
                position: absolute;
                top: 50px;
                left: 50%;
                transform: translateX(-50%);
                font-size: 10px;
                color: rgba(255, 255, 255, 0.8);
                background: rgba(0, 0, 0, 0.5);
                padding: 2px 8px;
                border-radius: 8px;
                backdrop-filter: blur(5px);
                border: 1px solid rgba(0, 212, 255, 0.3);
                white-space: nowrap;
              }
            }

            &.secondary {
              .node-core {
                width: 20px;
                height: 20px;
                background: rgba(0, 255, 155, 0.6);
                border: 1px solid rgba(0, 255, 155, 0.8);
                border-radius: 50%;
                box-shadow: 0 0 10px rgba(0, 255, 155, 0.4);
                animation: nodePulse 2s ease-in-out infinite;
                animation-delay: 0.5s;
              }
            }

            .node-pulse {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 100%;
              height: 100%;
              border: 1px solid rgba(0, 212, 255, 0.5);
              border-radius: 50%;
              animation: pulseRing 2s ease-out infinite;
            }
          }
        }
      }

      // 连接网络
      .connection-matrix {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 3;

        .matrix-svg {
          width: 100%;
          height: 100%;

          .data-flow {
            stroke-dasharray: 10 5;
            animation: flowMove 2s linear infinite;

            &.normal {
              stroke-dasharray: 8 4;
              animation-duration: 2s;
            }

            &.alert {
              stroke-dasharray: 6 3;
              animation-duration: 1s;
            }
          }

          .flow-particle {
            animation: particleMove 3s ease-in-out infinite;
          }
        }
      }

      // 状态指示器
      .status-indicators {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 15;
        pointer-events: none;

        .indicator-group {
          position: absolute;
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 11px;
          color: rgba(255, 255, 255, 0.7);
          background: rgba(0, 0, 0, 0.5);
          padding: 6px 12px;
          border-radius: 15px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);

          &.top-left {
            top: 20px;
            left: 20px;
          }

          &.top-right {
            top: 20px;
            right: 20px;
          }

          &.bottom-left {
            bottom: 20px;
            left: 20px;
          }

          &.bottom-right {
            bottom: 20px;
            right: 20px;
          }

          .status-light {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);

            &.active {
              background: #00ff9b;
              box-shadow: 0 0 10px rgba(0, 255, 155, 0.6);
              animation: statusBlink 2s ease-in-out infinite;
            }

            &.warning {
              background: #ffaa00;
              box-shadow: 0 0 10px rgba(255, 170, 0, 0.6);
              animation: statusBlink 1s ease-in-out infinite;
            }
          }
        }
      }

      // 浮动信息面板
      .floating-panels {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 20;
        pointer-events: none;

        .info-panel {
          position: absolute;
          background: rgba(0, 20, 40, 0.9);
          border: 1px solid rgba(0, 212, 255, 0.4);
          border-radius: 8px;
          padding: 12px;
          backdrop-filter: blur(15px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
          min-width: 120px;

          &.panel-1 {
            top: 60px;
            left: 60px;
            animation: panelFloat 4s ease-in-out infinite;
          }

          &.panel-2 {
            bottom: 60px;
            right: 60px;
            animation: panelFloat 4s ease-in-out infinite;
            animation-delay: 2s;
          }

          .panel-header {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 8px;
            font-weight: 600;
            border-bottom: 1px solid rgba(0, 212, 255, 0.3);
            padding-bottom: 4px;
          }

          .panel-content {
            .threat-item {
              display: flex;
              justify-content: space-between;
              font-size: 10px;
              margin: 4px 0;
              padding: 2px 0;

              &.high {
                color: #ff6464;
              }

              &.medium {
                color: #ffaa00;
              }

              &.low {
                color: #00ff9b;
              }
            }

            .process-bar {
              width: 100%;
              height: 6px;
              background: rgba(255, 255, 255, 0.1);
              border-radius: 3px;
              overflow: hidden;
              margin: 6px 0;

              .bar-fill {
                height: 100%;
                width: 85%;
                background: linear-gradient(90deg, #00ff9b, #00d4ff);
                border-radius: 3px;
                animation: progressPulse 2s ease-in-out infinite;
              }
            }

            .process-text {
              font-size: 10px;
              color: rgba(255, 255, 255, 0.7);
              text-align: center;
            }
          }
        }
      }
    }
  }
}

// 新增动画效果
@keyframes gridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

@keyframes consoleGlow {
  0% {
    opacity: 0.6;
    transform: scale(1);
  }
  100% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes radarSweep {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes threatPulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.7;
  }
}

@keyframes domeRotate {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes energyFlow {
  0%,
  100% {
    opacity: 0.3;
    transform: scaleY(1);
  }
  50% {
    opacity: 1;
    transform: scaleY(1.2);
  }
}

@keyframes nodePulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes pulseRing {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

@keyframes flowMove {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: 20;
  }
}

@keyframes particleMove {
  0%,
  100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

@keyframes statusBlink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes panelFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes progressPulse {
  0%,
  100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

// 右侧面板
.right-panel {
  width: 380px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px 0;

  .panel-card {
    background: linear-gradient(
      135deg,
      rgba(0, 30, 60, 0.4) 0%,
      rgba(0, 50, 100, 0.3) 50%,
      rgba(0, 20, 40, 0.4) 100%
    );
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 15px;
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.8), transparent);
    }

    &:hover {
      border-color: rgba(0, 212, 255, 0.4);
      box-shadow: 0 8px 25px rgba(0, 212, 255, 0.15);
    }

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      padding-bottom: 12px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      .card-icon {
        font-size: 16px;
        color: #00d4ff;
      }

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #ffffff;
        flex: 1;
        margin-left: 12px;
      }

      .card-controls {
        .control-btn {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.6);
          cursor: pointer;
          transition: color 0.3s ease;

          &:hover {
            color: #00d4ff;
          }
        }
      }
    }

    // 安全运营合规样式
    &.compliance-panel {
      .compliance-score {
        display: flex;
        align-items: center;
        gap: 20px;

        .score-circle {
          position: relative;
          width: 100px;
          height: 100px;
          border-radius: 50%;
          background: radial-gradient(
            circle,
            rgba(0, 212, 255, 0.2) 0%,
            rgba(0, 212, 255, 0.05) 70%,
            transparent 100%
          );
          border: 3px solid rgba(0, 212, 255, 0.3);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          .score-value {
            font-size: 28px;
            font-weight: 700;
            color: #00d4ff;
            font-family: 'Courier New', monospace;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
          }

          .score-unit {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            margin-top: -5px;
          }

          .score-label {
            position: absolute;
            bottom: -25px;
            font-size: 11px;
            color: rgba(255, 255, 255, 0.8);
            white-space: nowrap;
          }
        }

        .score-details {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 12px;

          .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);

            .detail-label {
              font-size: 12px;
              color: rgba(255, 255, 255, 0.7);
            }

            .detail-value {
              font-size: 16px;
              font-weight: 600;
              color: #ffffff;
              font-family: 'Courier New', monospace;
            }

            .detail-unit {
              font-size: 10px;
              color: rgba(255, 255, 255, 0.5);
              margin-left: 4px;
            }
          }
        }
      }
    }

    // 应用订阅趋势样式
    &.trend-panel {
      .trend-chart {
        position: relative;

        .chart-svg {
          width: 100%;
          height: 120px;
          border-radius: 8px;
          background: rgba(0, 0, 0, 0.2);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .chart-labels {
          display: flex;
          justify-content: space-between;
          margin-top: 8px;
          padding: 0 10px;

          .label {
            font-size: 10px;
            color: rgba(255, 255, 255, 0.6);
          }
        }
      }
    }

    // 热门应用TOP样式
    &.top-apps-panel {
      .top-apps-list {
        .app-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid rgba(255, 255, 255, 0.05);

          &:last-child {
            border-bottom: none;
          }

          .app-rank {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;

            .rank-badge {
              padding: 2px 8px;
              background: linear-gradient(135deg, #00d4ff, #5b8fff);
              border-radius: 12px;
              font-size: 10px;
              font-weight: 600;
              color: #ffffff;
            }

            .app-name {
              font-size: 12px;
              color: #ffffff;
              font-weight: 500;
            }
          }

          .app-usage {
            display: flex;
            align-items: center;
            gap: 2px;

            .usage-value {
              font-size: 14px;
              font-weight: 600;
              color: #00ff9b;
              font-family: 'Courier New', monospace;
            }

            .usage-unit {
              font-size: 10px;
              color: rgba(255, 255, 255, 0.5);
            }
          }
        }
      }
    }

    // 背景装饰元素
    .background-decorations {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 3;

      .grid-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
          linear-gradient(rgba(0, 212, 255, 0.05) 1px, transparent 1px),
          linear-gradient(90deg, rgba(0, 212, 255, 0.05) 1px, transparent 1px);
        background-size: 50px 50px;
        animation: gridMove 20s linear infinite;
      }

      .floating-particles {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;

        .particle {
          position: absolute;
          width: 3px;
          height: 3px;
          background: #00d4ff;
          border-radius: 50%;
          opacity: 0.6;
          animation: floatParticle 8s ease-in-out infinite;

          &:nth-child(1) {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
            background: #00d4ff;
          }

          &:nth-child(2) {
            top: 40%;
            left: 80%;
            animation-delay: 1.3s;
            background: #00ff9b;
          }

          &:nth-child(3) {
            top: 70%;
            left: 20%;
            animation-delay: 2.6s;
            background: #ffbb55;
          }

          &:nth-child(4) {
            top: 30%;
            left: 60%;
            animation-delay: 3.9s;
            background: #ff6b9d;
          }

          &:nth-child(5) {
            top: 80%;
            left: 70%;
            animation-delay: 5.2s;
            background: #00d4ff;
          }

          &:nth-child(6) {
            top: 15%;
            left: 40%;
            animation-delay: 6.5s;
            background: #00ff9b;
          }
        }
      }

      .corner-decorations {
        .corner-lines {
          position: absolute;
          width: 60px;
          height: 60px;
          border: 2px solid rgba(0, 212, 255, 0.3);

          &.top-left {
            top: 20px;
            left: 20px;
            border-right: none;
            border-bottom: none;
            border-radius: 10px 0 0 0;
          }

          &.top-right {
            top: 20px;
            right: 20px;
            border-left: none;
            border-bottom: none;
            border-radius: 0 10px 0 0;
          }

          &.bottom-left {
            bottom: 20px;
            left: 20px;
            border-right: none;
            border-top: none;
            border-radius: 0 0 0 10px;
          }

          &.bottom-right {
            bottom: 20px;
            right: 20px;
            border-left: none;
            border-top: none;
            border-radius: 0 0 10px 0;
          }
        }
      }
    }

    // 动画定义
    @keyframes lineFlow {
      0%,
      100% {
        opacity: 0.3;
        transform: scaleX(0.8);
      }
      50% {
        opacity: 1;
        transform: scaleX(1);
      }
    }

    @keyframes pulse {
      0%,
      100% {
        opacity: 0.6;
        transform: scale(1);
      }
      50% {
        opacity: 1;
        transform: scale(1.05);
      }
    }

    @keyframes platformPulse {
      0%,
      100% {
        opacity: 0.3;
        transform: scale(1);
      }
      50% {
        opacity: 0.6;
        transform: scale(1.1);
      }
    }

    @keyframes sphereRotate {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }

    @keyframes ringRotate {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }

    @keyframes serverBlink {
      0%,
      100% {
        opacity: 1;
      }
      50% {
        opacity: 0.3;
      }
    }

    @keyframes dataFlow {
      0%,
      100% {
        opacity: 0.4;
        transform: scale(0.8);
      }
      50% {
        opacity: 1;
        transform: scale(1.2);
      }
    }

    @keyframes screenFlicker {
      0%,
      100% {
        opacity: 0.8;
      }
      50% {
        opacity: 1;
      }
    }

    @keyframes dataFlowLine {
      0% {
        stroke-dashoffset: 0;
      }
      100% {
        stroke-dashoffset: 24;
      }
    }

    @keyframes particleMove {
      0% {
        opacity: 0;
        transform: scale(0);
      }
      10% {
        opacity: 1;
        transform: scale(1);
      }
      90% {
        opacity: 1;
        transform: scale(1) translateX(100px);
      }
      100% {
        opacity: 0;
        transform: scale(0) translateX(150px);
      }
    }

    @keyframes gridMove {
      0% {
        transform: translate(0, 0);
      }
      100% {
        transform: translate(50px, 50px);
      }
    }

    @keyframes floatParticle {
      0%,
      100% {
        transform: translateY(0px) translateX(0px);
        opacity: 0.3;
      }
      25% {
        transform: translateY(-15px) translateX(5px);
        opacity: 0.8;
      }
      50% {
        transform: translateY(-25px) translateX(-5px);
        opacity: 1;
      }
      75% {
        transform: translateY(-15px) translateX(5px);
        opacity: 0.8;
      }
    }

    // 响应式设计
    @media (max-width: 1600px) {
      .security-dashboard {
        .core-metrics {
          gap: 60px;

          .metric-item {
            padding: 16px 24px;

            .metric-icon {
              width: 40px;
              height: 40px;
              font-size: 20px;
            }

            .metric-content {
              .metric-value {
                font-size: 28px;
              }
            }
          }
        }

        .dashboard-main {
          gap: 30px;

          .left-panel,
          .right-panel {
            width: 320px;
          }

          .center-visualization {
            .security-command-center {
              transform: scale(0.9);
            }
          }
        }
      }
    }

    @media (max-width: 1200px) {
      .security-dashboard {
        .dashboard-header {
          height: 100px;
          padding: 0 30px;

          .platform-title-container {
            .platform-title {
              font-size: 28px;
              gap: 12px;

              .title-separator {
                font-size: 24px;
              }
            }

            .title-subtitle {
              font-size: 12px;
              gap: 8px;

              .subtitle-version {
                padding: 2px 8px;
                font-size: 10px;
              }
            }

            .title-status-indicators {
              gap: 16px;

              .status-indicator {
                font-size: 10px;

                .indicator-dot {
                  width: 6px;
                  height: 6px;
                }
              }
            }
          }

          .header-info {
            .current-time {
              .time-value {
                font-size: 14px;
              }
            }
          }
        }

        .core-metrics {
          gap: 40px;
          margin: 0 30px;

          .metric-item {
            padding: 12px 20px;

            .metric-content {
              .metric-value {
                font-size: 24px;
              }

              .metric-label {
                font-size: 12px;
              }
            }
          }
        }

        .dashboard-main {
          padding: 0 30px;
          gap: 20px;

          .left-panel,
          .right-panel {
            width: 280px;
          }

          .center-visualization {
            .security-command-center {
              transform: scale(0.8);
            }
          }
        }
      }
    }

    @media (max-width: 768px) {
      .security-dashboard {
        .dashboard-header {
          height: auto;
          min-height: 120px;
          padding: 20px;
          flex-direction: column;
          gap: 15px;
          justify-content: center;

          .platform-title-container {
            .title-decorative-elements {
              max-width: 300px;
              gap: 12px;

              .title-icon {
                font-size: 18px;
              }
            }

            .platform-title {
              font-size: 24px;
              gap: 8px;
              flex-direction: column;
              text-align: center;

              .title-separator {
                display: none;
              }
            }

            .title-subtitle {
              font-size: 11px;
              gap: 6px;
              flex-direction: column;

              .subtitle-version {
                padding: 2px 6px;
                font-size: 9px;
              }
            }

            .title-status-indicators {
              gap: 12px;
              flex-wrap: wrap;
              justify-content: center;

              .status-indicator {
                font-size: 9px;

                .indicator-dot {
                  width: 5px;
                  height: 5px;
                }
              }
            }
          }

          .header-info {
            position: static;
            transform: none;
            align-items: center;
            .system-status {
              display: none;
            }

            .current-time {
              padding: 4px 12px;

              .time-value {
                font-size: 12px;
              }
            }
          }
        }

        .core-metrics {
          flex-direction: column;
          gap: 15px;
          margin: 0 20px;

          .metric-item {
            padding: 10px 16px;

            .metric-icon {
              width: 35px;
              height: 35px;
              font-size: 16px;
            }

            .metric-content {
              .metric-value {
                font-size: 20px;
              }

              .metric-label {
                font-size: 11px;
              }
            }
          }
        }

        .dashboard-main {
          flex-direction: column;
          height: auto;
          padding: 0 20px;
          gap: 15px;

          .left-panel,
          .right-panel {
            width: 100%;

            .panel-card {
              padding: 15px;

              .card-header {
                margin-bottom: 15px;

                .card-title {
                  font-size: 14px;
                }
              }
            }
          }

          .center-visualization {
            height: 300px;
            order: -1;

            .security-command-center {
              transform: scale(0.6);
            }
          }
        }
      }
    }
  }

  // 新增动画效果
  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
      filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.8));
    }
    50% {
      transform: scale(1.1);
      filter: drop-shadow(0 0 20px rgba(0, 212, 255, 1));
    }
  }

  @keyframes separatorGlow {
    0% {
      text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
    }
    100% {
      text-shadow:
        0 0 20px rgba(0, 212, 255, 0.8),
        0 0 30px rgba(0, 212, 255, 0.4);
    }
  }

  @keyframes statusPulse {
    0%,
    100% {
      transform: scale(1);
      box-shadow: 0 0 10px rgba(0, 255, 155, 0.6);
    }
    50% {
      transform: scale(1.2);
      box-shadow: 0 0 15px rgba(0, 255, 155, 0.8);
    }
  }

  @keyframes statusRipple {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    100% {
      transform: scale(2);
      opacity: 0;
    }
  }
}
