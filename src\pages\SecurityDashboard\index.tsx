import React, { useEffect, useState } from 'react';
import './index.less';

const SecurityDashboard: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date());

  // 核心指标数据
  const [metrics, setMetrics] = useState({
    // 顶部核心指标
    totalAlerts: 202,
    processedEvents: 926,
    resolvedIssues: 328,

    // 模型训练分析
    currentTasks: 849,
    queueWaiting: 87.5,

    // 算力资源分析
    cpuUsage: 73,
    gpuUsage: 52,
    memoryUsage: 82,

    // 模型调用排行
    modelRanking: [
      { name: '威胁检测-v2', calls: 153, percentage: 0.96, trend: 8.976 },
      { name: 'OCR文字识别-v1', calls: 129, percentage: 0.92, trend: 7.516 },
      { name: '自然语言-v1', calls: 233, percentage: 0.16, trend: 6.455 },
      { name: '分析-v5', calls: 89, percentage: 0.26, trend: 5.532 },
      { name: '分析-v1', calls: 278, percentage: 0.12, trend: 3.038 },
    ],

    // 安全运营合规
    complianceScore: 96,
    securityLevel: 19,
    riskLevel: 2.9,
    responseTime: 92,

    // 应用订阅趋势
    trendData: [
      { time: '9.15', value: 45 },
      { time: '9.16', value: 52 },
      { time: '9.17', value: 48 },
      { time: '9.18', value: 65 },
      { time: '9.19', value: 58 },
      { time: '9.20', value: 72 },
      { time: '9.21', value: 68 },
      { time: '9.22', value: 75 },
      { time: '9.23', value: 82 },
      { time: '9.24', value: 78 },
    ],

    // 热门应用TOP
    topApps: [
      { rank: 1, name: '全景监控分析', usage: 9.976 },
      { rank: 2, name: '威胁情报检测', usage: 3.678 },
      { rank: 3, name: '高级威胁分析', usage: 2.436 },
      { rank: 4, name: '安全态势感知', usage: 1.892 },
      { rank: 5, name: '情报数据分析', usage: 852 },
    ],
  });

  // 时间更新
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // 模拟数据更新
  useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(prev => ({
        ...prev,
        totalAlerts: prev.totalAlerts + Math.floor(Math.random() * 2),
        processedEvents: prev.processedEvents + Math.floor(Math.random() * 3),
        cpuUsage: Math.round(Math.max(50, Math.min(90, prev.cpuUsage + (Math.random() - 0.5) * 4))),
        gpuUsage: Math.round(Math.max(40, Math.min(80, prev.gpuUsage + (Math.random() - 0.5) * 3))),
        memoryUsage: Math.round(
          Math.max(60, Math.min(95, prev.memoryUsage + (Math.random() - 0.5) * 2))
        ),
      }));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className='security-dashboard'>
      {/* 顶部标题区域 */}
      <div className='dashboard-header'>
        <div className='header-decorative-lines'>
          <div className='line line-left'></div>
          <div className='line line-center'></div>
          <div className='line line-right'></div>
        </div>
        <div className='platform-title-container'>
          <div className='title-decorative-elements'>
            <div className='title-line title-line-left'></div>
            <div className='title-icon'>🛡️</div>
            <div className='title-line title-line-right'></div>
          </div>
          <h1 className='platform-title'>
            <span className='title-main'>安全赋能</span>
            <span className='title-separator'>·</span>
            <span className='title-sub'>一体化运营平台</span>
          </h1>
          <div className='title-subtitle'>
            <span className='subtitle-text'>AI-Powered Security Operations Center</span>
            <div className='subtitle-version'>v2.0</div>
          </div>
          <div className='title-status-indicators'>
            <div className='status-indicator active'>
              <div className='indicator-dot'></div>
              <span>系统运行正常</span>
            </div>
            <div className='status-indicator'>
              <div className='indicator-dot'></div>
              <span>AI引擎活跃</span>
            </div>
            <div className='status-indicator'>
              <div className='indicator-dot'></div>
              <span>实时监控中</span>
            </div>
          </div>
        </div>
        <div className='header-info'>
          <div className='system-status'>
            <span className='status-icon'>🔊</span>
            <span className='status-text'>系统运行：[7天运行] 07-24运行正常，节点运行正常...</span>
          </div>
          <div className='current-time'>
            <span className='time-label'>当前时间</span>
            <span className='time-value'>
              {currentTime.toLocaleTimeString('zh-CN', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
              })}
            </span>
          </div>
        </div>
      </div>

      {/* 核心指标区域 */}
      <div className='core-metrics'>
        <div className='metric-item'>
          <div className='metric-icon alert-icon'>⚠️</div>
          <div className='metric-content'>
            <div className='metric-value'>{metrics.totalAlerts}</div>
            <div className='metric-label'>告警总数</div>
          </div>
        </div>
        <div className='metric-item'>
          <div className='metric-icon process-icon'>⚡</div>
          <div className='metric-content'>
            <div className='metric-value'>{metrics.processedEvents}</div>
            <div className='metric-label'>已处理事件数</div>
          </div>
        </div>
        <div className='metric-item'>
          <div className='metric-icon resolve-icon'>✅</div>
          <div className='metric-content'>
            <div className='metric-value'>{metrics.resolvedIssues}</div>
            <div className='metric-label'>应用总数</div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className='dashboard-main'>
        {/* 左侧面板 */}
        <div className='left-panel'>
          {/* 模型训练分析 */}
          <div className='panel-card training-analysis'>
            <div className='card-header'>
              <div className='card-icon'>▶️</div>
              <div className='card-title'>模型训练分析</div>
              <div className='card-controls'>
                <span className='control-btn'>🔊</span>
              </div>
            </div>
            <div className='card-content'>
              <div className='metric-grid'>
                <div className='metric-item'>
                  <div className='metric-label'>当前任务</div>
                  <div className='metric-value highlight'>{metrics.currentTasks}</div>
                  <div className='metric-unit'>个</div>
                </div>
                <div className='metric-item'>
                  <div className='metric-label'>当前等待</div>
                  <div className='metric-value'>{metrics.queueWaiting}</div>
                  <div className='metric-unit'>%</div>
                </div>
              </div>
            </div>
          </div>

          {/* 算力资源分析 */}
          <div className='panel-card resource-analysis'>
            <div className='card-header'>
              <div className='card-icon'>▶️</div>
              <div className='card-title'>算力资源分析</div>
              <div className='card-controls'>
                <span className='control-btn'>🔊</span>
              </div>
            </div>
            <div className='card-content'>
              <div className='resource-meters'>
                <div className='meter-item'>
                  <div className='meter-label'>CPU</div>
                  <div className='circular-meter'>
                    <div className='meter-value'>{Math.round(metrics.cpuUsage)}</div>
                    <div className='meter-unit'>%</div>
                    <svg className='meter-circle' viewBox='0 0 100 100'>
                      <circle
                        cx='50'
                        cy='50'
                        r='45'
                        fill='none'
                        stroke='rgba(0, 212, 255, 0.2)'
                        strokeWidth='8'
                      />
                      <circle
                        cx='50'
                        cy='50'
                        r='45'
                        fill='none'
                        stroke='#00d4ff'
                        strokeWidth='8'
                        strokeDasharray={`${Math.round(metrics.cpuUsage) * 2.83} 283`}
                        strokeLinecap='round'
                        transform='rotate(-90 50 50)'
                      />
                    </svg>
                  </div>
                </div>
                <div className='meter-item'>
                  <div className='meter-label'>GPU</div>
                  <div className='circular-meter'>
                    <div className='meter-value'>{Math.round(metrics.gpuUsage)}</div>
                    <div className='meter-unit'>%</div>
                    <svg className='meter-circle' viewBox='0 0 100 100'>
                      <circle
                        cx='50'
                        cy='50'
                        r='45'
                        fill='none'
                        stroke='rgba(0, 255, 155, 0.2)'
                        strokeWidth='8'
                      />
                      <circle
                        cx='50'
                        cy='50'
                        r='45'
                        fill='none'
                        stroke='#00ff9b'
                        strokeWidth='8'
                        strokeDasharray={`${Math.round(metrics.gpuUsage) * 2.83} 283`}
                        strokeLinecap='round'
                        transform='rotate(-90 50 50)'
                      />
                    </svg>
                  </div>
                </div>
                <div className='meter-item'>
                  <div className='meter-label'>内存</div>
                  <div className='circular-meter'>
                    <div className='meter-value'>{Math.round(metrics.memoryUsage)}</div>
                    <div className='meter-unit'>%</div>
                    <svg className='meter-circle' viewBox='0 0 100 100'>
                      <circle
                        cx='50'
                        cy='50'
                        r='45'
                        fill='none'
                        stroke='rgba(255, 187, 85, 0.2)'
                        strokeWidth='8'
                      />
                      <circle
                        cx='50'
                        cy='50'
                        r='45'
                        fill='none'
                        stroke='#ffbb55'
                        strokeWidth='8'
                        strokeDasharray={`${Math.round(metrics.memoryUsage) * 2.83} 283`}
                        strokeLinecap='round'
                        transform='rotate(-90 50 50)'
                      />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 模型调用排行 */}
          <div className='panel-card model-ranking'>
            <div className='card-header'>
              <div className='card-icon'>▶️</div>
              <div className='card-title'>模型调用排行</div>
              <div className='card-controls'>
                <span className='control-btn'>🔊</span>
              </div>
            </div>
            <div className='card-content'>
              <div className='ranking-list'>
                {metrics.modelRanking.map((item, index) => (
                  <div key={index} className='ranking-item'>
                    <div className='rank-info'>
                      <span className='rank-number'>{index + 1}</span>
                      <span className='model-name'>{item.name}</span>
                    </div>
                    <div className='rank-stats'>
                      <span className='calls'>{item.calls}</span>
                      <span className='unit'>ms</span>
                      <span className='percentage'>{item.percentage}</span>
                      <span className='unit'>%</span>
                      <span className='trend'>{item.trend}</span>
                      <span className='unit'>%</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 中央安全态势感知中心 */}
        <div className='center-visualization'>
          <div className='security-command-center'>
            {/* 背景网格 */}
            <div className='grid-background'>
              <div className='grid-lines'></div>
              <div className='grid-dots'></div>
            </div>

            {/* 中央控制台 */}
            <div className='central-console'>
              <div className='console-base'>
                <div className='base-glow'></div>
                <div className='base-surface'></div>
              </div>

              {/* 全息显示屏 */}
              <div className='holographic-display'>
                <div className='holo-screen'>
                  <div className='screen-frame'></div>
                  <div className='screen-content'>
                    <div className='threat-radar'>
                      <div className='radar-center'></div>
                      <div className='radar-sweep'></div>
                      <div className='radar-rings'>
                        <div className='ring'></div>
                        <div className='ring'></div>
                        <div className='ring'></div>
                      </div>
                      <div className='threat-dots'>
                        <div className='threat-dot high'></div>
                        <div className='threat-dot medium'></div>
                        <div className='threat-dot low'></div>
                        <div className='threat-dot low'></div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className='holo-label'>威胁态势雷达</div>
              </div>
            </div>

            {/* 安全防护罩 */}
            <div className='security-dome'>
              <div className='dome-structure'>
                <div className='dome-layer layer-1'></div>
                <div className='dome-layer layer-2'></div>
                <div className='dome-layer layer-3'></div>
              </div>
              <div className='dome-energy-flows'>
                <div className='energy-stream stream-1'></div>
                <div className='energy-stream stream-2'></div>
                <div className='energy-stream stream-3'></div>
                <div className='energy-stream stream-4'></div>
              </div>
            </div>

            {/* 数据节点群 */}
            <div className='data-nodes'>
              <div className='node-cluster cluster-left'>
                <div className='node primary'>
                  <div className='node-core'></div>
                  <div className='node-pulse'></div>
                  <div className='node-label'>威胁检测</div>
                </div>
                <div className='node secondary'>
                  <div className='node-core'></div>
                  <div className='node-pulse'></div>
                </div>
                <div className='node secondary'>
                  <div className='node-core'></div>
                  <div className='node-pulse'></div>
                </div>
              </div>

              <div className='node-cluster cluster-right'>
                <div className='node primary'>
                  <div className='node-core'></div>
                  <div className='node-pulse'></div>
                  <div className='node-label'>响应处置</div>
                </div>
                <div className='node secondary'>
                  <div className='node-core'></div>
                  <div className='node-pulse'></div>
                </div>
                <div className='node secondary'>
                  <div className='node-core'></div>
                  <div className='node-pulse'></div>
                </div>
              </div>

              <div className='node-cluster cluster-top'>
                <div className='node primary'>
                  <div className='node-core'></div>
                  <div className='node-pulse'></div>
                  <div className='node-label'>AI分析</div>
                </div>
                <div className='node secondary'>
                  <div className='node-core'></div>
                  <div className='node-pulse'></div>
                </div>
              </div>

              <div className='node-cluster cluster-bottom'>
                <div className='node primary'>
                  <div className='node-core'></div>
                  <div className='node-pulse'></div>
                  <div className='node-label'>数据融合</div>
                </div>
                <div className='node secondary'>
                  <div className='node-core'></div>
                  <div className='node-pulse'></div>
                </div>
              </div>
            </div>

            {/* 连接网络 */}
            <div className='connection-matrix'>
              <svg className='matrix-svg' viewBox='0 0 600 400'>
                <defs>
                  <linearGradient id='dataStream' x1='0%' y1='0%' x2='100%' y2='0%'>
                    <stop offset='0%' stopColor='rgba(0, 255, 155, 0)' />
                    <stop offset='50%' stopColor='rgba(0, 255, 155, 1)' />
                    <stop offset='100%' stopColor='rgba(0, 255, 155, 0)' />
                  </linearGradient>
                  <linearGradient id='alertStream' x1='0%' y1='0%' x2='100%' y2='0%'>
                    <stop offset='0%' stopColor='rgba(255, 100, 100, 0)' />
                    <stop offset='50%' stopColor='rgba(255, 100, 100, 1)' />
                    <stop offset='100%' stopColor='rgba(255, 100, 100, 0)' />
                  </linearGradient>
                  <filter id='matrixGlow'>
                    <feGaussianBlur stdDeviation='3' result='coloredBlur' />
                    <feMerge>
                      <feMergeNode in='coloredBlur' />
                      <feMergeNode in='SourceGraphic' />
                    </feMerge>
                  </filter>
                </defs>

                {/* 主要数据流 */}
                <path
                  className='data-flow normal'
                  d='M 100 200 L 300 200'
                  stroke='url(#dataStream)'
                  strokeWidth='2'
                  filter='url(#matrixGlow)'
                />
                <path
                  className='data-flow normal'
                  d='M 300 200 L 500 200'
                  stroke='url(#dataStream)'
                  strokeWidth='2'
                  filter='url(#matrixGlow)'
                />
                <path
                  className='data-flow normal'
                  d='M 300 100 L 300 200'
                  stroke='url(#dataStream)'
                  strokeWidth='2'
                  filter='url(#matrixGlow)'
                />
                <path
                  className='data-flow normal'
                  d='M 300 200 L 300 300'
                  stroke='url(#dataStream)'
                  strokeWidth='2'
                  filter='url(#matrixGlow)'
                />

                {/* 警报数据流 */}
                <path
                  className='data-flow alert'
                  d='M 150 150 L 300 200'
                  stroke='url(#alertStream)'
                  strokeWidth='2'
                  filter='url(#matrixGlow)'
                />
                <path
                  className='data-flow alert'
                  d='M 450 150 L 300 200'
                  stroke='url(#alertStream)'
                  strokeWidth='2'
                  filter='url(#matrixGlow)'
                />

                {/* 流动粒子 */}
                <circle className='flow-particle' r='2' fill='#00ff9b'>
                  <animateMotion dur='2s' repeatCount='indefinite'>
                    <mpath href='#normalFlow1' />
                  </animateMotion>
                </circle>
                <circle className='flow-particle' r='2' fill='#ff6464'>
                  <animateMotion dur='1.5s' repeatCount='indefinite'>
                    <mpath href='#alertFlow1' />
                  </animateMotion>
                </circle>
              </svg>
            </div>

            {/* 状态指示器 */}
            <div className='status-indicators'>
              <div className='indicator-group top-left'>
                <div className='status-light active'></div>
                <span>系统正常</span>
              </div>
              <div className='indicator-group top-right'>
                <div className='status-light warning'></div>
                <span>检测到威胁</span>
              </div>
              <div className='indicator-group bottom-left'>
                <div className='status-light active'></div>
                <span>AI引擎运行</span>
              </div>
              <div className='indicator-group bottom-right'>
                <div className='status-light active'></div>
                <span>防护激活</span>
              </div>
            </div>

            {/* 浮动信息面板 */}
            <div className='floating-panels'>
              <div className='info-panel panel-1'>
                <div className='panel-header'>实时威胁</div>
                <div className='panel-content'>
                  <div className='threat-item high'>高危: 3</div>
                  <div className='threat-item medium'>中危: 12</div>
                  <div className='threat-item low'>低危: 28</div>
                </div>
              </div>

              <div className='info-panel panel-2'>
                <div className='panel-header'>处理状态</div>
                <div className='panel-content'>
                  <div className='process-bar'>
                    <div className='bar-fill'></div>
                  </div>
                  <div className='process-text'>处理中: 85%</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧面板 */}
        <div className='right-panel'>
          {/* 安全运营合规 */}
          <div className='panel-card compliance-panel'>
            <div className='card-header'>
              <div className='card-icon'>▶️</div>
              <div className='card-title'>安全运营合规</div>
              <div className='card-controls'>
                <span className='control-btn'>🔊</span>
              </div>
            </div>
            <div className='card-content'>
              <div className='compliance-score'>
                <div className='score-circle'>
                  <div className='score-value'>{metrics.complianceScore}</div>
                  <div className='score-unit'>%</div>
                  <div className='score-label'>安全合规</div>
                </div>
                <div className='score-details'>
                  <div className='detail-item'>
                    <span className='detail-label'>计算资源</span>
                    <span className='detail-value'>{metrics.securityLevel}</span>
                    <span className='detail-unit'>个</span>
                  </div>
                  <div className='detail-item'>
                    <span className='detail-label'>风险等级</span>
                    <span className='detail-value'>{metrics.riskLevel}</span>
                    <span className='detail-unit'>个</span>
                  </div>
                  <div className='detail-item'>
                    <span className='detail-label'>响应时间</span>
                    <span className='detail-value'>{metrics.responseTime}</span>
                    <span className='detail-unit'>个</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 应用订阅趋势 */}
          <div className='panel-card trend-panel'>
            <div className='card-header'>
              <div className='card-icon'>▶️</div>
              <div className='card-title'>应用订阅趋势</div>
              <div className='card-controls'>
                <span className='control-btn'>🔊</span>
              </div>
            </div>
            <div className='card-content'>
              <div className='trend-chart'>
                <svg className='chart-svg' viewBox='0 0 300 120'>
                  <defs>
                    <linearGradient id='trendGradient' x1='0%' y1='0%' x2='0%' y2='100%'>
                      <stop offset='0%' stopColor='rgba(0, 212, 255, 0.8)' />
                      <stop offset='100%' stopColor='rgba(0, 212, 255, 0.1)' />
                    </linearGradient>
                  </defs>

                  {/* 趋势线 */}
                  <path
                    d={`M ${metrics.trendData
                      .map((point, index) => `${index * 30} ${120 - point.value}`)
                      .join(' L ')}`}
                    fill='none'
                    stroke='#00d4ff'
                    strokeWidth='2'
                    strokeLinecap='round'
                  />

                  {/* 填充区域 */}
                  <path
                    d={`M ${metrics.trendData
                      .map((point, index) => `${index * 30} ${120 - point.value}`)
                      .join(' L ')} L ${(metrics.trendData.length - 1) * 30} 120 L 0 120 Z`}
                    fill='url(#trendGradient)'
                  />

                  {/* 数据点 */}
                  {metrics.trendData.map((point, index) => (
                    <circle
                      key={index}
                      cx={index * 30}
                      cy={120 - point.value}
                      r='3'
                      fill='#00d4ff'
                      stroke='#ffffff'
                      strokeWidth='1'
                    />
                  ))}
                </svg>

                {/* X轴标签 */}
                <div className='chart-labels'>
                  {metrics.trendData.map((point, index) => (
                    <span key={index} className='label'>
                      {point.time}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* 热门应用TOP */}
          <div className='panel-card top-apps-panel'>
            <div className='card-header'>
              <div className='card-icon'>▶️</div>
              <div className='card-title'>热门应用TOP</div>
              <div className='card-controls'>
                <span className='control-btn'>🔊</span>
              </div>
            </div>
            <div className='card-content'>
              <div className='top-apps-list'>
                {metrics.topApps.map((app, index) => (
                  <div key={index} className='app-item'>
                    <div className='app-rank'>
                      <span className='rank-badge'>No.{app.rank}</span>
                      <span className='app-name'>{app.name}</span>
                    </div>
                    <div className='app-usage'>
                      <span className='usage-value'>{app.usage}</span>
                      <span className='usage-unit'>%</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 背景装饰元素 */}
      <div className='background-decorations'>
        <div className='grid-overlay'></div>
        <div className='floating-particles'>
          <div className='particle'></div>
          <div className='particle'></div>
          <div className='particle'></div>
          <div className='particle'></div>
          <div className='particle'></div>
          <div className='particle'></div>
        </div>
        <div className='corner-decorations'>
          <div className='corner-lines top-left'></div>
          <div className='corner-lines top-right'></div>
          <div className='corner-lines bottom-left'></div>
          <div className='corner-lines bottom-right'></div>
        </div>
      </div>
    </div>
  );
};

export default SecurityDashboard;
