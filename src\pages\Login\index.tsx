import React, { useEffect } from 'react';
import { Form, Input, Button, Checkbox, message } from 'antd';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useUserStore } from '../../store/userStore';
import { getToken } from '../../utils/token';

import LegalLinks from '../../components/LegalLinks';
import './index.less';

interface LoginFormValues {
  userAccount: string;
  userPassword: string;
  userRemember: boolean;
}

const Login: React.FC = () => {
  const [form] = Form.useForm<LoginFormValues>();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { login, isLoading, error } = useUserStore();

  // 从Cookie恢复记住的登录信息
  const restoreRememberedData = () => {
    const savedUsername = localStorage.getItem('remembered_username');
    const savedPassword = localStorage.getItem('remembered_password');
    if (savedUsername && savedPassword) {
      form.setFieldsValue({
        userAccount: savedUsername,
        userPassword: savedPassword,
        userRemember: true,
      });
    }
  };

  // 组件初始化
  useEffect(() => {
    restoreRememberedData();
  }, []);

  // 显示错误信息
  useEffect(() => {
    if (error) {
      message.error(error);
    }
  }, [error]);

  // 处理登录
  const handleLogin = async (values: LoginFormValues) => {
    try {
      // 处理记住密码
      if (values.userRemember && values.userAccount && values.userPassword) {
        localStorage.setItem('remembered_username', values.userAccount);
        localStorage.setItem('remembered_password', values.userPassword);
      } else {
        localStorage.removeItem('remembered_username');
        localStorage.removeItem('remembered_password');
      }

      // 执行登录（userStore 内部会生成 requestId）
      await login(values.userAccount, values.userPassword);

      message.success('登录成功！');

      // 处理重定向
      const redirectUrl = searchParams.get('redirectUrl');
      const ssoToken = getToken();

      if (redirectUrl && ssoToken) {
        console.log('SSO重定向到:', redirectUrl);
        console.log('携带token:', ssoToken);
        // SSO重定向，带上token
        window.location.href = `${redirectUrl}?ssoToken=${ssoToken}`;
      } else {
        console.log('重定向信息:', { redirectUrl, ssoToken });
        // 普通重定向
        const redirect = searchParams.get('redirect') || '/platform/';
        navigate(redirect);
      }
    } catch (error: any) {
      console.error('登录失败:', error);
    }
  };

  // 处理登录失败
  const handleLoginFailed = (errorInfo: any) => {
    console.log('表单验证失败:', errorInfo);
  };

  return (
    <div className='login-page'>
      <div className='logo-container'>
        <img src='/platform/images/logo.svg' alt='logo' />
      </div>

      <div className='login-container'>
        <h2 className='login-title'>登录账号</h2>

        <Form form={form} name='login' onFinish={handleLogin} autoComplete='off' size='large'>
          <Form.Item
            name='userAccount'
            rules={[
              { required: true, message: '请输入用户名' },
              {
                pattern: /^[a-z][a-z0-9\-]*[a-z0-9]$/,
                message: '用户名只能由小写字母、数字、-组成',
              },
            ]}
          >
            <Input placeholder='请输入用户名' />
          </Form.Item>

          <Form.Item name='userPassword' rules={[{ required: true, message: '请输入密码' }]}>
            <Input.Password placeholder='请输入密码' />
          </Form.Item>

          <div style={{ margin: '28px 0 12px' }}>
            <Form.Item name='userRemember' valuePropName='checked' initialValue={true} noStyle>
              <Checkbox>记住密码</Checkbox>
            </Form.Item>
          </div>

          <Form.Item>
            <Button
              type='primary'
              htmlType='submit'
              loading={isLoading}
              block
              className='login-button'
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        <div className='legal-links-container'>
          <LegalLinks theme='light' size='small' />
        </div>
      </div>
    </div>
  );
};

export default Login;
